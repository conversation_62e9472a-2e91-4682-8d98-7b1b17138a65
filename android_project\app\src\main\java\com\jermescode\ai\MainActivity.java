package com.jermescode.ai;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.DownloadManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.DownloadListener;
import android.webkit.GeolocationPermissions;
import android.webkit.JavascriptInterface;
import android.webkit.PermissionRequest;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.core.splashscreen.SplashScreen;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.HashMap;

import org.json.JSONObject;
import org.json.JSONException;

import android.webkit.WebResourceResponse;
import java.io.ByteArrayInputStream;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final int REQUEST_CAMERA_PERMISSION = 1001;
    private static final int REQUEST_STORAGE_PERMISSION = 1002;
    private static final int REQUEST_FILE_PICKER = 1003;
    private static final int REQUEST_CAMERA_CAPTURE = 1004;
    private static final int LOCAL_SERVER_PORT = 8080;

    private WebView webView;
    private ProgressBar progressBar;
    private ValueCallback<Uri[]> fileUploadCallback;
    private String cameraPhotoPath;
    // private LocalWebServer localWebServer;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // Handle the splash screen transition
        SplashScreen splashScreen = SplashScreen.installSplashScreen(this);
        
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        setupLocalServer();
        setupWebView();
        requestPermissions();
    }

    private void initializeViews() {
        webView = findViewById(R.id.webview);
        progressBar = findViewById(R.id.progress_bar);
    }

    private void setupLocalServer() {
        // Create and start local HTTP server
        // localWebServer = new LocalWebServer(this, LOCAL_SERVER_PORT);
        // localWebServer.startServer();

        // Log.i(TAG, "Local server started at: " + localWebServer.getServerUrl());

        // Load the web application via HTTP server (this solves CORS issues)
        // String serverUrl = localWebServer.getServerUrl();
        // webView.loadUrl(serverUrl);

        // Temporary fallback: Load directly from assets
        webView.loadUrl("file:///android_asset/www/index.html");
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void setupWebView() {
        // Enable debugging for WebView (helps with CORS issues)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        WebSettings webSettings = webView.getSettings();

        // Enable JavaScript
        webSettings.setJavaScriptEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);

        // Enable DOM storage
        webSettings.setDomStorageEnabled(true);
        webSettings.setDatabaseEnabled(true);

        // Enable file access - CRITICAL for CORS bypass
        webSettings.setAllowFileAccess(true);
        webSettings.setAllowContentAccess(true);
        webSettings.setAllowFileAccessFromFileURLs(true);
        webSettings.setAllowUniversalAccessFromFileURLs(true);

        // Enable mixed content - CRITICAL for HTTPS APIs
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
        }

        // Set user agent to mimic Chrome browser
        String userAgent = webSettings.getUserAgentString();
        if (!userAgent.contains("Chrome")) {
            // Make WebView appear as Chrome browser to bypass restrictions
            userAgent = "Mozilla/5.0 (Linux; Android " + Build.VERSION.RELEASE + "; " + Build.MODEL + ") " +
                       "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36 JermesaCodeAi/1.0";
            webSettings.setUserAgentString(userAgent);
        }

        // Enable all browser-like features
        webSettings.setSupportZoom(true);
        webSettings.setBuiltInZoomControls(true);
        webSettings.setDisplayZoomControls(false);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setUseWideViewPort(true);

        // Enable caching and storage
        webSettings.setCacheMode(WebSettings.LOAD_DEFAULT);
        //webSettings.setAppCacheEnabled(true);
        //webSettings.setAppCachePath(getApplicationContext().getCacheDir().getAbsolutePath());

        // Enable geolocation
        webSettings.setGeolocationEnabled(true);

        // Enable media playback
        webSettings.setMediaPlaybackRequiresUserGesture(false);

        // Set WebView client
        webView.setWebViewClient(new CustomWebViewClient());
        webView.setWebChromeClient(new CustomWebChromeClient());

        // Add JavaScript interface for API calls to bypass CORS
        webView.addJavascriptInterface(new AndroidAPIBridge(), "AndroidAPI");

        // Set download listener
        webView.setDownloadListener(new CustomDownloadListener());
    }

    private void requestPermissions() {
        String[] permissions = {
            Manifest.permission.CAMERA,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.WRITE_EXTERNAL_STORAGE
        };
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissions = new String[]{
                Manifest.permission.CAMERA,
                Manifest.permission.READ_MEDIA_IMAGES,
                Manifest.permission.READ_MEDIA_VIDEO,
                Manifest.permission.READ_MEDIA_AUDIO
            };
        }
        
        boolean needsPermission = false;
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                needsPermission = true;
                break;
            }
        }
        
        if (needsPermission) {
            ActivityCompat.requestPermissions(this, permissions, REQUEST_STORAGE_PERMISSION);
        }
    }

    private class CustomWebViewClient extends WebViewClient {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
            String url = request.getUrl().toString();

            // Handle external URLs
            if (url.startsWith("http://") || url.startsWith("https://")) {
                if (!url.contains("localhost") && !url.contains("127.0.0.1")) {
                    // Open external URLs in browser
                    Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
                    startActivity(intent);
                    return true;
                }
            }

            return false;
        }

        // No need to intercept requests since we're using HTTP server

        @Override
        public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            progressBar.setVisibility(View.VISIBLE);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            progressBar.setVisibility(View.GONE);

            Log.i(TAG, "Page loaded successfully via HTTP server: " + url);
        }
    }

    private class CustomWebChromeClient extends WebChromeClient {
        @Override
        public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
            Log.d(TAG, "Console: " + consoleMessage.message() + " -- From line "
                    + consoleMessage.lineNumber() + " of " + consoleMessage.sourceId());
            return true;
        }

        @Override
        public void onProgressChanged(WebView view, int newProgress) {
            super.onProgressChanged(view, newProgress);
            progressBar.setProgress(newProgress);
        }

        @Override
        public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback,
                                       FileChooserParams fileChooserParams) {
            fileUploadCallback = filePathCallback;

            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.addCategory(Intent.CATEGORY_OPENABLE);
            intent.setType("*/*");

            // Create camera intent
            Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
            if (cameraIntent.resolveActivity(getPackageManager()) != null) {
                File photoFile = null;
                try {
                    photoFile = createImageFile();
                } catch (IOException ex) {
                    Log.e(TAG, "Error occurred while creating the File", ex);
                }

                if (photoFile != null) {
                    cameraPhotoPath = "file:" + photoFile.getAbsolutePath();
                    Uri photoURI = FileProvider.getUriForFile(MainActivity.this,
                            "com.jermescode.ai.fileprovider", photoFile);
                    cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                }
            }

            // Create chooser intent
            Intent chooserIntent = Intent.createChooser(intent, "Select File");
            chooserIntent.putExtra(Intent.EXTRA_INITIAL_INTENTS, new Intent[]{cameraIntent});

            startActivityForResult(chooserIntent, REQUEST_FILE_PICKER);
            return true;
        }

        @Override
        public void onPermissionRequest(PermissionRequest request) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                request.grant(request.getResources());
            }
        }

        @Override
        public void onGeolocationPermissionsShowPrompt(String origin,
                                                      GeolocationPermissions.Callback callback) {
            callback.invoke(origin, true, false);
        }
    }

    private class CustomDownloadListener implements DownloadListener {
        @Override
        public void onDownloadStart(String url, String userAgent, String contentDisposition,
                                  String mimeType, long contentLength) {
            DownloadManager.Request request = new DownloadManager.Request(Uri.parse(url));
            request.setMimeType(mimeType);

            String filename = "download";
            if (contentDisposition != null && contentDisposition.contains("filename=")) {
                filename = contentDisposition.substring(contentDisposition.indexOf("filename=") + 9);
                filename = filename.replaceAll("\"", "");
            }

            request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, filename);
            request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED);
            request.setVisibleInDownloadsUi(true);

            DownloadManager downloadManager = (DownloadManager) getSystemService(Context.DOWNLOAD_SERVICE);
            downloadManager.enqueue(request);

            Toast.makeText(MainActivity.this, "Downloading " + filename, Toast.LENGTH_SHORT).show();
        }
    }

    private File createImageFile() throws IOException {
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = getExternalFilesDir(Environment.DIRECTORY_PICTURES);
        return File.createTempFile(imageFileName, ".jpg", storageDir);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == REQUEST_FILE_PICKER) {
            if (fileUploadCallback == null) return;

            Uri[] results = null;

            if (resultCode == Activity.RESULT_OK) {
                if (data == null) {
                    // Camera capture
                    if (cameraPhotoPath != null) {
                        results = new Uri[]{Uri.parse(cameraPhotoPath)};
                    }
                } else {
                    // File picker
                    String dataString = data.getDataString();
                    if (dataString != null) {
                        results = new Uri[]{Uri.parse(dataString)};
                    }
                }
            }

            fileUploadCallback.onReceiveValue(results);
            fileUploadCallback = null;
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions,
                                         @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == REQUEST_STORAGE_PERMISSION || requestCode == REQUEST_CAMERA_PERMISSION) {
            boolean allGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false;
                    break;
                }
            }

            if (!allGranted) {
                Toast.makeText(this, "Some permissions were denied. App functionality may be limited.",
                        Toast.LENGTH_LONG).show();
            }
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK && webView.canGoBack()) {
            webView.goBack();
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    protected void onDestroy() {
        // if (localWebServer != null) {
        //     localWebServer.stopServer();
        // }
        if (webView != null) {
            webView.destroy();
        }
        super.onDestroy();
    }

    /**
     * JavaScript interface to handle API calls from WebView
     * This bypasses CORS restrictions by making HTTP calls from native Android code
     */
    public class AndroidAPIBridge {

        @JavascriptInterface
        public void makeApiCall(String url, String method, String headers, String body, String callbackId) {
            new Thread(() -> {
                try {
                    String result = performHttpRequest(url, method, headers, body);

                    // Call JavaScript callback with success result
                    runOnUiThread(() -> {
                        String jsCode = String.format(
                            "if (window.apiCallbacks && window.apiCallbacks['%s']) { " +
                            "window.apiCallbacks['%s']({success: true, data: %s, status: 200}); " +
                            "delete window.apiCallbacks['%s']; }",
                            callbackId, callbackId, escapeJsonString(result), callbackId
                        );
                        webView.evaluateJavascript(jsCode, null);
                    });

                } catch (Exception e) {
                    Log.e(TAG, "API call failed", e);

                    // Call JavaScript callback with error result
                    runOnUiThread(() -> {
                        String jsCode = String.format(
                            "if (window.apiCallbacks && window.apiCallbacks['%s']) { " +
                            "window.apiCallbacks['%s']({success: false, error: %s}); " +
                            "delete window.apiCallbacks['%s']; }",
                            callbackId, callbackId, escapeJsonString(e.getMessage()), callbackId
                        );
                        webView.evaluateJavascript(jsCode, null);
                    });
                }
            }).start();
        }

        @JavascriptInterface
        public void makeStreamingApiCall(String url, String method, String headers, String body, String callbackId) {
            new Thread(() -> {
                try {
                    performStreamingHttpRequest(url, method, headers, body, callbackId);
                } catch (Exception e) {
                    Log.e(TAG, "Streaming API call failed", e);

                    // Call JavaScript callback with error result
                    runOnUiThread(() -> {
                        String jsCode = String.format(
                            "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                            "window.streamCallbacks['%s']({success: false, error: %s}); " +
                            "delete window.streamCallbacks['%s']; }",
                            callbackId, callbackId, escapeJsonString(e.getMessage()), callbackId
                        );
                        webView.evaluateJavascript(jsCode, null);
                    });
                }
            }).start();
        }
    }

    private String performHttpRequest(String urlString, String method, String headersJson, String body) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod(method);
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(60000); // 60 seconds

            // Parse and set headers
            if (headersJson != null && !headersJson.isEmpty()) {
                try {
                    JSONObject headers = new JSONObject(headersJson);
                    for (String key : headers.keys()) {
                        connection.setRequestProperty(key, headers.getString(key));
                    }
                } catch (JSONException e) {
                    Log.w(TAG, "Failed to parse headers: " + e.getMessage());
                }
            }

            // Set body for POST/PUT requests
            if (body != null && !body.isEmpty() && ("POST".equals(method) || "PUT".equals(method))) {
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(body.getBytes("UTF-8"));
                    os.flush();
                }
            }

            // Read response
            int responseCode = connection.getResponseCode();
            InputStream inputStream = responseCode >= 200 && responseCode < 300
                ? connection.getInputStream()
                : connection.getErrorStream();

            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line).append('\n');
                }
            }

            return response.toString();

        } finally {
            connection.disconnect();
        }
    }

    private void performStreamingHttpRequest(String urlString, String method, String headersJson, String body, String callbackId) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();

        try {
            connection.setRequestMethod(method);
            connection.setConnectTimeout(30000); // 30 seconds
            connection.setReadTimeout(120000); // 2 minutes for streaming

            // Parse and set headers
            if (headersJson != null && !headersJson.isEmpty()) {
                try {
                    JSONObject headers = new JSONObject(headersJson);
                    for (String key : headers.keys()) {
                        connection.setRequestProperty(key, headers.getString(key));
                    }
                } catch (JSONException e) {
                    Log.w(TAG, "Failed to parse headers: " + e.getMessage());
                }
            }

            // Set body for POST/PUT requests
            if (body != null && !body.isEmpty() && ("POST".equals(method) || "PUT".equals(method))) {
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(body.getBytes("UTF-8"));
                    os.flush();
                }
            }

            // Read streaming response
            int responseCode = connection.getResponseCode();
            InputStream inputStream = responseCode >= 200 && responseCode < 300
                ? connection.getInputStream()
                : connection.getErrorStream();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    final String chunk = line;

                    // Send each chunk to JavaScript
                    runOnUiThread(() -> {
                        String jsCode = String.format(
                            "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                            "window.streamCallbacks['%s']({success: true, chunk: %s}); }",
                            callbackId, callbackId, escapeJsonString(chunk)
                        );
                        webView.evaluateJavascript(jsCode, null);
                    });
                }

                // Signal end of stream
                runOnUiThread(() -> {
                    String jsCode = String.format(
                        "if (window.streamCallbacks && window.streamCallbacks['%s']) { " +
                        "window.streamCallbacks['%s']({success: true, done: true}); " +
                        "delete window.streamCallbacks['%s']; }",
                        callbackId, callbackId, callbackId
                    );
                    webView.evaluateJavascript(jsCode, null);
                });
            }

        } finally {
            connection.disconnect();
        }
    }

    private String escapeJsonString(String str) {
        if (str == null) return "null";
        return "\"" + str.replace("\\", "\\\\")
                        .replace("\"", "\\\"")
                        .replace("\n", "\\n")
                        .replace("\r", "\\r")
                        .replace("\t", "\\t") + "\"";
    }

}
